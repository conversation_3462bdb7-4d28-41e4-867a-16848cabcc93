import { useSelector, useDispatch } from "react-redux";
import { RootState } from "../../store";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { styled } from "@linaria/react";
import { MessageTable } from "./MessageTable";
import { CommentsTable } from "./CommentsTable";
import FavoritesPage from "../Favorites";
import HistoryPage from "../HistoryPage";
import { useTheme } from "../../utils/useTheme";
import { But<PERSON>, Divider, Flex } from "antd";
import { FullscreenOutlined } from "@ant-design/icons";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { setHomeSectionMask } from "../../store/features";
import { MyTooltip } from "../../components/atoms/MyTooltip";

const SortableItem = (props) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const homeSectionMask = useSelector(
    (state: RootState) => state.mask.homeSectionMask
  );
  const pinned = useSelector((state: RootState) => state.pinned.pinned);

  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({
      id: props.id,
    });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const getContent = (key: string) => {
    switch (key) {
      case "pinned":
        return (
          <InfoDiv
            theme={theme}
            style={
              homeSectionMask === "pinned"
                ? {
                    border: "1px solid red",
                    zIndex: "6000",
                    position: "relative",
                  }
                : {}
            }
          >
            <>
              <Flex
                className="header-wrapper"
                justify="space-between"
                align="center"
              >
                <h4 {...listeners}>{t("Pinned")}</h4>
                {pinned?.length > 4 && !homeSectionMask && (
                  <div className="actions">
                    <Link to="/pinned" className="view-all">
                      <MyTooltip title="View all">
                        <FullscreenOutlined />
                      </MyTooltip>
                    </Link>
                  </div>
                )}
                {homeSectionMask === "pinned" && (
                  <Flex gap={10}>
                    <Button
                      className="breadcrumb-button cancel cancel-button"
                      onClick={() => {
                        setTimeout(() => {
                          dispatch(setHomeSectionMask(null));
                        }, 200);
                      }}
                    >
                      {t("Cancel")}
                    </Button>
                    <Button
                      className="breadcrumb-button save-button"
                      onClick={() => {}}
                    >
                      {t("Save")}
                    </Button>
                  </Flex>
                )}
              </Flex>
              <Divider />
              <article
                style={{ maxHeight: 400, overflow: "auto", marginBottom: 20 }}
              >
                <FavoritesPage isHome={true} height="250px" />
              </article>
            </>
          </InfoDiv>
        );
      case "comments":
        return (
          <InfoDiv
            theme={theme}
            style={
              homeSectionMask === "comments"
                ? {
                    border: "1px solid red",
                    zIndex: "6000",
                    position: "relative",
                  }
                : {}
            }
          >
            <CommentsTable listeners={listeners} />
          </InfoDiv>
        );
      case "messages":
        return (
          <InfoDiv
            theme={theme}
            style={
              homeSectionMask === "messages"
                ? {
                    border: "1px solid red",
                    zIndex: "6000",
                    position: "relative",
                  }
                : {}
            }
          >
            <MessageTable listeners={listeners} />
          </InfoDiv>
        );
      case "history":
        return (
          <InfoDiv
            theme={theme}
            style={
              homeSectionMask === "history"
                ? {
                    border: "1px solid red",
                    zIndex: "6000",
                    position: "relative",
                  }
                : {}
            }
          >
            <>
              <Flex
                className="header-wrapper"
                justify="space-between"
                align="center"
              >
                <h4 {...listeners}>{t("HISTORY")}</h4>
                {!homeSectionMask && (
                  <div className="actions">
                    <Link to="/history" className="view-all">
                      <MyTooltip title="View all">
                        <FullscreenOutlined />
                      </MyTooltip>
                    </Link>
                  </div>
                )}
                {homeSectionMask === "history" && (
                  <Flex gap={10}>
                    <Button
                      className="breadcrumb-button cancel cancel-button"
                      onClick={() => {
                        setTimeout(() => {
                          dispatch(setHomeSectionMask(null));
                        }, 200);
                      }}
                    >
                      {t("Cancel")}
                    </Button>
                    <Button
                      className="breadcrumb-button save-button"
                      onClick={() => {}}
                    >
                      {t("Save")}
                    </Button>
                  </Flex>
                )}
              </Flex>
              <Divider />
              <article>
                <HistoryPage isHome={true} height="260px" />
              </article>
            </>
          </InfoDiv>
        );
    }
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes}>
      {getContent(props.id)}
    </div>
  );
};

export { SortableItem };

const InfoDiv = styled.div<{ theme?: any }>`
  border: 1px solid #eee;
  padding: 0px 20px 10px 20px;
  border-radius: 10px;
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  & ::selection {
    background: transparent;
  }
  &::-moz-selection {
    background: transparent;
  }

  & .actions {
    display: flex;
    gap: 15px;
    font-size: 17px;
    color: ${({ theme }) => theme.colorPrimary};
  }

  & .view-all {
    color: ${({ theme }) => theme.colorPrimary};
    display: flex;
    align-items: center;
    justify-content: center;

    & .anticon {
      font-size: 17px;
      width: 17px;
      height: 17px;
      cursor: pointer;
    }

    &:hover {
      opacity: 0.8;
    }
  }

  & article,
  .data-table-wrapper {
    height: 100%;
  }

  & .p-datatable {
    display: flex;
    height: 100%;
    flex-direction: column;
  }

  & .p-datatable-wrapper {
    flex: 1;
  }
`;
