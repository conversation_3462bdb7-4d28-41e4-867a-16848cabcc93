import React from "react";
import { render } from "@testing-library/react";
import { transformObjectPath } from "./transformObjectPath";

// Mock i18n
jest.mock("../../utils/i18n", () => ({
  i18n: {
    t: (key: string) => key === "Trash" ? "Trash" : key,
  },
}));

describe("transformObjectPath", () => {
  it("returns empty string for empty path", () => {
    const result = transformObjectPath("", false);
    expect(result).toBe("");
  });

  it("returns empty string for null path", () => {
    const result = transformObjectPath(null as any, false);
    expect(result).toBe("");
  });

  it("transforms path with curly braces to JSX with SVG separators", () => {
    const result = transformObjectPath("Root{123}Category{456}Item", false);
    expect(React.isValidElement(result)).toBe(true);
    
    const { container } = render(result as React.ReactElement);
    expect(container.textContent).toContain("Root");
    expect(container.textContent).toContain("Category");
    expect(container.querySelectorAll("svg")).toHaveLength(1); // One separator between Root and Category
  });

  it("handles trash items correctly", () => {
    const result = transformObjectPath("Root{123}Category{456}Item", true);
    expect(React.isValidElement(result)).toBe(true);
    
    const { container } = render(result as React.ReactElement);
    expect(container.textContent).toContain("Trash");
    expect(container.textContent).toContain("Root");
    expect(container.textContent).toContain("Category");
    expect(container.querySelectorAll("svg")).toHaveLength(2); // Trash > Root > Category
  });

  it("returns Trash span for single part trash items", () => {
    const result = transformObjectPath("SingleItem", true);
    expect(React.isValidElement(result)).toBe(true);
    
    const { container } = render(result as React.ReactElement);
    expect(container.textContent).toBe("Trash");
    expect(container.querySelectorAll("svg")).toHaveLength(0);
  });

  it("returns empty string for single part non-trash items", () => {
    const result = transformObjectPath("SingleItem", false);
    expect(result).toBe("");
  });

  it("handles paths with hash and pipe characters", () => {
    const result = transformObjectPath("Root#Category|Item{123}Final", false);
    expect(React.isValidElement(result)).toBe(true);
    
    const { container } = render(result as React.ReactElement);
    expect(container.textContent).toContain("Root");
    expect(container.textContent).toContain("Category");
    expect(container.textContent).toContain("Item");
    // Should not contain "Final" as it's the last part
    expect(container.textContent).not.toContain("Final");
  });
});
