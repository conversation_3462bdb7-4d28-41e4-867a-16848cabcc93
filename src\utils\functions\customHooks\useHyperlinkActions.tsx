import { getParentIds } from "../getParentIds";
import { getHierarchyDetails, getNodeDetails } from "../../../services/node";
import { useDispatch, useSelector } from "react-redux";

import { RootState } from "../../../store";
import {
  setExpandedTrashKeys,
  setSelectedTrash,
  setTrashCollapsed,
} from "../../../store/features/trashcan";
import {
  setExpandedKeys,
  setRefreshBreadcrumbs,
} from "../../../store/features";
import { notification } from "antd";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { IHyperlinks } from "../../../interfaces";
import {
  setBottomNavbarOpen,
  setSelectedBottomNavbar,
} from "../../../store/features/navigation";
import { useQueryClient } from "react-query";
import {
  AUTHOR_USERS_TEMPLATE_ID,
  GET_HIERARCHY_DETAILS,
} from "../../../constants";
import { useCallback } from "react";

const useHyperlinkActions = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const { expandedKeys, mask } = useSelector((root: RootState) => root.sidebar);
  const workingVersionActive = useSelector(
    (state: RootState) => state.mask.workingVersion
  );

  const bottomNavigationMask = useSelector(
    (state: RootState) => state.mask.bottomDrawer
  );

  const expandedTrashKeys = useSelector(
    (root: RootState) => root.trash.expandedTrashKeys
  );

  const handleDQMTestClick = async (id, testId) => {
    if (mask || workingVersionActive || bottomNavigationMask) {
      return;
    }

    const parentPath = await getHierarchyDetails(id);
    const parentIds = getParentIds(parentPath.path);

    if (parentIds.length === 0) {
      notification.error({
        message: t("Unable to move to node!"),
        description: t("Node may be deleted!"),
      });
      return;
    }
    dispatch(setBottomNavbarOpen(true));
    dispatch(setSelectedBottomNavbar("test-execution"));
    setTimeout(() => {
      dispatch(setRefreshBreadcrumbs(true));
    }, 200);

    setTimeout(() => {
      dispatch(setExpandedKeys([...expandedKeys, ...parentIds]));
    }, 300);

    const queryParams = new URLSearchParams();
    queryParams.set("nodeId", id);
    if (parentPath?.menuId != "0") {
      queryParams.set("redirect", "true");
    }
    queryParams.set("testId", testId);

    if (location.pathname.includes("metamodel")) {
      navigate(`/settings/metamodel/-1?${queryParams.toString()}`);
    } else if (location.pathname.includes("data-sources")) {
      navigate(`/settings/datasources/-2?${queryParams.toString()}`);
    } else {
      navigate(`/details/${parentPath.menuId}?${queryParams.toString()}`);
    }
  };

  const handleHyperlinkClick = async (id, isAsset) => {
    if (mask || workingVersionActive || bottomNavigationMask) {
      return;
    }

    const parentPath = await getHierarchyDetails(id);
    const parentIds = getParentIds(parentPath.path);

    queryClient.setQueryData(
      [GET_HIERARCHY_DETAILS, id?.toString()],
      parentPath
    );

    if (parentIds.length === 0) {
      notification.error({
        message: t("Unable to move to node!"),
        description: t("Node may be deleted!"),
      });
      return;
    }
    dispatch(setBottomNavbarOpen(false));
    setTimeout(() => {
      dispatch(setRefreshBreadcrumbs(true));
    }, 200);

    setTimeout(() => {
      dispatch(setExpandedKeys([...expandedKeys, ...parentIds]));
    }, 300);

    const queryParams = new URLSearchParams();
    queryParams.set("nodeId", id);
    if (+parentPath?.menuId != 0) {
      queryParams.set("redirect", "true");
    }
    if (isAsset) {
      navigate(`/details/${parentPath.menuId}?${queryParams.toString()}`);
    } else if (location.pathname.includes("metamodel")) {
      navigate(`/settings/metamodel/-1?${queryParams.toString()}`);
    } else if (+parentPath.menuId === -5) {
      // Roles
      navigate(`/settings/roles/-5?${queryParams.toString()}`);
    } else {
      // Check if the target node is an author by fetching its details
      try {
        const nodeDetails = await getNodeDetails(id);
        if (nodeDetails?.templateId === AUTHOR_USERS_TEMPLATE_ID) {
          navigate(`/authors/${AUTHOR_USERS_TEMPLATE_ID}?${queryParams.toString()}`);
        } else {
          navigate(`/details/${parentPath.menuId}?${queryParams.toString()}`);
        }
      } catch (error) {
        // If node details fetch fails, use default route
        navigate(`/details/${parentPath.menuId}?${queryParams.toString()}`);
      }
    }
  };

  const handleTrashHyperlinkClick = useCallback(
    async (id) => {
      if (mask || workingVersionActive || bottomNavigationMask) {
        return;
      }

      dispatch(setTrashCollapsed(false));
      const parentPath = await getHierarchyDetails(id);
      const parentIds = getParentIds(parentPath.path);
      const nodeDetails = (await getNodeDetails(id)) as any;

      setTimeout(() => {
        dispatch(setExpandedTrashKeys([...expandedTrashKeys, ...parentIds]));
        dispatch(
          setSelectedTrash({
            keys: [id],
            info: [
              {
                id: nodeDetails?.id,
                name: nodeDetails?.name,
                parentId: nodeDetails?.parentId,
                templateId: nodeDetails?.templateId,
                isLeaf: nodeDetails?.isLeaf,
              },
            ],
          })
        );
      }, 300);
    },
    [bottomNavigationMask, mask, workingVersionActive]
  );

  const handleHyperlinkAction = (hyperlink: IHyperlinks) => {
    if (hyperlink.inTrash) {
      handleTrashHyperlinkClick(hyperlink.id);
    } else {
      handleHyperlinkClick(hyperlink.id, hyperlink.isAsset);
    }
  };
  return {
    handleTrashHyperlinkClick,
    handleHyperlinkClick,
    handleHyperlinkAction,
    handleDQMTestClick,
  };
};

export { useHyperlinkActions };
