import React from "react";
import { RightOutlined } from "@ant-design/icons";

interface PathSeparatorProps {
  className?: string;
  style?: React.CSSProperties;
}

export const PathSeparator: React.FC<PathSeparatorProps> = ({
  className,
  style
}) => {
  const defaultStyle: React.CSSProperties = {
    fontSize: "0.75em",
    color: "#8c8c8c", // Ant Design's gray-6 color
    flexShrink: 0,
    display: "inline-flex",
    alignItems: "center",
    ...style,
  };

  return (
    <RightOutlined
      className={className}
      style={defaultStyle}
    />
  );
};

export default PathSeparator;
