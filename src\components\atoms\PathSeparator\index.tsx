import React from "react";

interface PathSeparatorProps {
  className?: string;
  style?: React.CSSProperties;
}

export const PathSeparator: React.FC<PathSeparatorProps> = ({ 
  className, 
  style 
}) => {
  const defaultStyle: React.CSSProperties = {
    width: "0.75em",
    height: "0.75em",
    stroke: "rgba(0, 0, 0, 0.8)", // -20% lighter than pure black
    strokeWidth: "2",
    flexShrink: 0,
    display: "inline-block",
    ...style,
  };

  return (
    <svg
      className={className}
      style={defaultStyle}
      fill="none"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M9 5l7 7-7 7"
      />
    </svg>
  );
};

export default PathSeparator;
