import React from "react";

interface PathSeparatorProps {
  className?: string;
  style?: React.CSSProperties;
}

export const PathSeparator: React.FC<PathSeparatorProps> = ({
  className,
  style
}) => {
  const defaultStyle: React.CSSProperties = {
    width: "10px",
    height: "10px",
    color: "rgba(0, 0, 0, 0.4)", // Even lighter color
    flexShrink: 0,
    display: "inline-flex",
    alignItems: "center",
    marginLeft: "0.3em", // Equal left margin
    marginRight: "0.3em", // Equal right margin
    ...style,
  };

  return (
    <svg
      width="10"
      height="10"
      viewBox="0 0 10 10"
      className={`path-separator-color ${className || ""}`}
      style={defaultStyle}
    >
      <path
        d="M3.5 2L6.5 5L3.5 8"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />
    </svg>
  );
};

export default PathSeparator;
