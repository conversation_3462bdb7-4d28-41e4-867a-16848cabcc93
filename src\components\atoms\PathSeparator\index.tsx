import React from "react";
import { RightOutlined } from "@ant-design/icons";

interface PathSeparatorProps {
  className?: string;
  style?: React.CSSProperties;
}

export const PathSeparator: React.FC<PathSeparatorProps> = ({
  className,
  style
}) => {
  const defaultStyle: React.CSSProperties = {
    fontSize: "0.6em", // Smaller than path text
    color: "rgba(0, 0, 0, 0.6)", // Much lighter, lower contrast
    flexShrink: 0,
    display: "inline-flex",
    alignItems: "center",
    transform: "scaleY(0.6)", // Reduce height significantly
    fontWeight: "bold", // Make it look bolder
    ...style,
  };

  return (
    <RightOutlined
      className={className}
      style={defaultStyle}
    />
  );
};

export default PathSeparator;
