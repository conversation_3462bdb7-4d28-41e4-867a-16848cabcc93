import { styled } from "@linaria/react";
import { useTheme } from "../../../../utils/useTheme";
import { IAGColumns, MyTable } from "../../../organisms";
import {
  GET_HISTORY_DATA,
  GET_LOCAL_SETTINGS_KEY,
  getAttributeIcon,
} from "../../../../constants";
import { memo, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  getMetamodelHistory,
  getNodeHistory,
} from "../../../../services/history";
import { useNotification } from "../../../../utils/functions/customHooks";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { setBottomDrawerMask } from "../../../../store/features";
import { ILocalSettings } from "../../../../interfaces";
import { saveLocalSettings } from "../../../../services";
import { getAttributeTitleWidth } from "../../../../utils";
import { useLocation, useSearchParams } from "react-router-dom";
import { setTrashcanDrawerMask } from "../../../../store/features/trashcan";
import { withErrorBoundary } from "../../../../components/withErrorBoundary";
import { debounce } from "lodash";

const HistoryContainerBase = ({ id, displaySaveCancel, fromTrashcan }) => {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  const location = useLocation();

  const parentRef = useRef(null);
  const gridApiRef = useRef(null);

  const [height, setHeight] = useState(0);
  const [minTitleWidth, setMinTitleWidth] = useState(0);

  const { devMode, hiddenAttributes } = useSelector(
    (root: RootState) => root.globalSettings
  );
  const { selectedBottomNavbar } = useSelector(
    (state: RootState) => state.navigation
  );

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const COLUMNS = [
    {
      headerName: "Date & time",
      field: "timestamp",
      width: 200,
      cellRenderer: ({ data }) =>
        dayjs(data.timestamp).format("YYYY-MM-DD HH:mm"),
    },

    {
      headerName: "Operation",
      field: "message",
      minWidth: 200,
      flex: 1,
      cellRenderer: ({ data }) => {
        return t(data?.operation, data?.params);
      },
    },
    {
      headerName: "User",
      field: "user",
      width: 200,
    },

    {
      headerName: "Snapshot",
      field: "attributesValues",
      flex: 1,
      minWidth: 200,
      noSort: true,
      className: "attributes-data",
      cellRenderer: ({ data }) => {
        const attributeKeys = devMode
          ? Object.keys(data.attributesValues)
          : Object.keys(data.attributesValues).filter(
            (item) => !hiddenAttributes.includes(item)
          );

        return (
          <Content>
            {attributeKeys?.map((attribute, index) => (
              <div className="item" key={index}>
                <h6
                  className="history-title"
                  style={{ minWidth: minTitleWidth }}
                >
                  {attribute}
                </h6>
                {attribute === "Icon" ? (
                  <p>{getAttributeIcon(data.attributesValues[attribute])}</p>
                ) : attribute?.includes("WHERE") ? (
                  <p>{data.attributesValues[attribute] || "-"}</p>
                ) : (
                  <p
                    dangerouslySetInnerHTML={{
                      __html: data.attributesValues[attribute] || "-",
                    }}
                  ></p>
                )}
              </div>
            ))}
          </Content>
        );
      },
    },
  ] as IAGColumns[];

  const [columns, setColumns] = useState(COLUMNS);

  // get history data
  const { data, isLoading, isError } = useQuery(
    [GET_HISTORY_DATA, id],
    () =>
      location.pathname?.includes("metamodel")
        ? getMetamodelHistory(id)
        : getNodeHistory(id),
    {
      enabled: !!id && !searchParams.get("draft"),
      staleTime: Infinity,
    }
  );

  // calculate dynamic width for attribute title once attributes are loaded
  const calculateWidth = () => {
    const titles = document.querySelectorAll(".history-title") as any;

    titles.forEach((title) => {
      title.style.minWidth = `fit-content`;
    });

    const maxTitleWidth = getAttributeTitleWidth(".history-title");
    setMinTitleWidth(maxTitleWidth);
    titles.forEach((title) => {
      title.style.minWidth = `${maxTitleWidth}px`;
    });
  };

  useEffect(() => {
    calculateWidth();

    setTimeout(() => {
      calculateWidth();
    }, 300);
  }, [data, displaySaveCancel, selectedBottomNavbar]);

  // dynamic height for table
  useEffect(() => {
    const parent = parentRef.current;
    if (!parent) return;

    const updateHeight = debounce((entries) => {
      for (const entry of entries) {
        setHeight(entry.contentRect.height);
      }
      calculateWidth();
    }, 100);

    const observer = new ResizeObserver(updateHeight);
    observer.observe(parent);

    return () => {
      observer.disconnect();
    };
  }, []);

  const detectChange = () => {
    if (fromTrashcan) {
      dispatch(setTrashcanDrawerMask(true));
    } else {
      dispatch(setBottomDrawerMask(true));
    }
  };

  // Add ag-grid event listeners to monitor virtualization changes
  useEffect(() => {
    if (!gridApiRef.current) return;

    const updateWidthOnChange = debounce(() => {
      // Recalculate width when displayed rows change due to virtualization
      calculateWidth();
    }, 1);

    // Add event listeners for proper ag-grid virtualization events
    gridApiRef.current.addEventListener('displayedRowsChanged', updateWidthOnChange);
    gridApiRef.current.addEventListener('bodyScroll', updateWidthOnChange);
    gridApiRef.current.addEventListener('viewportChanged', updateWidthOnChange);

    return () => {
      if (gridApiRef.current) {
        gridApiRef.current.removeEventListener('displayedRowsChanged', updateWidthOnChange);
        gridApiRef.current.removeEventListener('bodyScroll', updateWidthOnChange);
        gridApiRef.current.removeEventListener('viewportChanged', updateWidthOnChange);
      }
    };
  }, [gridApiRef.current]);

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }
    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.historyDrawer &&
      localSettingsData?.body[0]?.value?.historyDrawer?.columns
    ) {
      const pinned =
        localSettingsData?.body[0]?.value?.historyDrawer?.pinned || [];
      const sort = localSettingsData?.body[0]?.value?.historyDrawer?.sort || [];

      const allColumns = [];
      localSettingsData.body[0].value.historyDrawer.columns?.forEach(
        (column) => {
          if (!column) {
            return;
          }
          const index = COLUMNS.findIndex((item) => item.field === column);
          allColumns.push({
            ...COLUMNS[index],
            pinned: pinned?.includes(column) ? "left" : null,
            sort: sort?.find((val) => val.colId === column)?.sort || null,
          });
        }
      );
      setColumns(allColumns);
    } else {
      setColumns(COLUMNS);
    }
  }, [localSettingsData]);

  const handleCancel = () => {
    setTimeout(() => {
      if (fromTrashcan) {
        dispatch(setTrashcanDrawerMask(false));
      } else {
        dispatch(setBottomDrawerMask(false));
      }
    }, 200);
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setBottomDrawerMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setBottomDrawerMask(false));
    },
  });

  // save table details to local settings
  const handleSave = (newColumns: string[], filters, sort, pinned) => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        historyDrawer: {
          columns: newColumns,
          filters: filters,
          sort: sort,
          pinned: pinned,
        },
      },
    };
    mutation.mutate(request);
  };

  return (
    <Wrapper
      ref={parentRef}
      theme={theme}
      style={{ border: displaySaveCancel ? "1px solid red" : "none" }}
    >
      {contextHolder}
      <MyTable
        isError={isError}
        excelFileName="history"
        height={`${height - 50}px`}
        data={[...(data || [])].map((d, index) => ({ ...d, id: index }))}
        loading={isLoading}
        columns={columns}
        onGridReady={(gridRef) => {
          gridApiRef.current = gridRef?.api;
        }}
        emptyMessage="No history"
        detectChange={detectChange}
        displaySaveCancel={displaySaveCancel}
        onCancelClick={handleCancel}
        saveLoading={mutation.isLoading}
        onSaveClick={handleSave}
        initialFilters={
          localSettingsData?.body[0]?.value?.historyDrawer?.filters || {}
        }
      />
    </Wrapper>
  );
};

const HistoryContainer = withErrorBoundary(
  memo(HistoryContainerBase),
  "error.generic"
);

export { HistoryContainer };

const Wrapper = styled.div<{ theme: any }>`
  height: 100%;
  overflow: hidden;
  display: flex;
  width: 100%;
  padding: 10px;
  margin-bottom: 20px;

  & > div {
    width: 100%;
  }

  & img {
    object-fit: contain;
  }
  & .p-datatable {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  & .data-table-wrapper {
    overflow: hidden;
  }

  & .p-datatable-wrapper {
    flex: 1;
    max-height: unset !important;
  }
  & .p-datatable-header {
    overflow-x: auto;
    overflow-y: hidden;
  }

  & td {
    overflow: auto;
  }
  & td.attributes-data {
    overflow: auto;
  }
  & .item {
    display: flex;
    align-items: stretch;
    gap: 6px;
    border: 0.5px solid #eaeaea;
    border-radius: 4px;
    margin-bottom: 6px;
    min-width: fit-content;

    & h6 {
      font-size: 13px;
      font-weight: 400;
      background-color: ${({ theme }) => theme.bgAttributes};
      border-right: 1px solid #eaeaea;
      text-align: left;
      color: ${({ theme }) => theme.colorPrimary};
      padding: 6px;
      display: flex;
      align-items: baseline;
    }
    & p {
      font-size: 13px;
      padding: 6px 6px 6px 0px;
      word-break: break-all;
      flex: 1;
      text-align: left;
      min-width: 60px;
      overflow: auto;
    }
  }

  & .ant-table-header > table,
  .ant-table-body > table {
    width: fit-content !important;
  }

  & td {
    cursor: auto !important;
  }

  & .table-wrapper {
    width: 100%;
  }

  & table {
    height: auto !important;
  }
  position: relative;
  & .anticon-export {
    font-size: 17px;
    margin-left: 10px;
  }
  & .ant-avatar {
    border-radius: 10px;
  }
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
  overflow: auto;
  padding-top: 4px;
  padding-bottom: 4px;
`;
