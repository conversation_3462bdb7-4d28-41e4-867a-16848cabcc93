import { PushpinFilled } from "@ant-design/icons";
import { PageTableSkeleton } from "../../components/atoms/SkeletonComponents";
import { Dropdown, Flex, Button } from "antd";

import { useTheme } from "../../utils/useTheme";
import { Suspense, useEffect, useState } from "react";
import { Wrapper } from "../HistoryPage/style";
import { BreadCrumb, DetailsContainer, MyTable } from "../../components";
import { styled } from "@linaria/react";
import { Link } from "react-router-dom";
import { GET_LOCAL_SETTINGS_KEY, NODES_MENU_ITEMS } from "../../constants";
import { useMutation, useQueryClient } from "react-query";
import { ILocalSettings } from "../../interfaces";
import {
  useHyperlinkActions,
  useNotification,
  useTemplateActions,
} from "../../utils/functions/customHooks";
import { useTranslation } from "react-i18next";
import { saveLocalSettings } from "../../services";
import { useDispatch, useSelector } from "react-redux";
import {
  setBreadcrumb,
  setExpandedKeys,
  setMask,
  setParentBreadcrumbs,
  setRefreshBreadcrumbs,
} from "../../store/features";
import { RootState } from "../../store";
import { getHierarchyDetails } from "../../services/node";
import { Button as PrimeButton } from "primereact/button";
import { setPinned } from "../../store/features/pinned";
import { getParentIds } from "../../utils/functions/getParentIds";
import { transformObjectPath } from "../../utils/functions/transformObjectPath";
import dayjs from "dayjs";

interface FavoritesPageProps {
  isHome?: boolean;
  height?: string;
}

const FavoritesPage: React.FC<FavoritesPageProps> = ({
  isHome = false,
  height = "calc(100vh - 200px)",
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const [filters, setFilters] = useState({});
  const [sort, setSort] = useState([]);
  const [pinnedColumns, setPinnedColumns] = useState([]);
  const [trigger, setTrigger] = useState(0);
  const [pinnedItems, setPinnedItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [isDetailsOpen, setDetailsOpen] = useState(null);

  const mask = useSelector((state: RootState) => state.sidebar.mask);
  const pinned = useSelector((state: RootState) => state.pinned.pinned);
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();
  const { handleTrashHyperlinkClick } = useHyperlinkActions();
  const { getTemplateName, getTemplateIcon } = useTemplateActions();

  const COLUMNS = [
    {
      headerName: "Name",
      field: "name",
      minWidth: 100,
      flex: 1,
      cellRenderer: ({ data }) => {
        return (
          <Dropdown
            menu={{
              items: NODES_MENU_ITEMS,
              onClick: (e) =>
                handleNodeClick(e.key, data.id, data.name, data.parentId),
            }}
            trigger={["contextMenu"]}
          >
            <NameWrapper
              to={
                data?.inTrash
                  ? null
                  : `/details/${data.parentId}?nodeId=${data.id}`
              }
              onClick={() => {
                if (data?.inTrash) {
                  handleTrashHyperlinkClick(data.id);
                } else handleFavoritesClick(data.id);
              }}
              className={data?.inTrash ? "trash-hyperlink" : ""}
            >
              {data?.inTrash ? <s>{data.name}</s> : data.name}
            </NameWrapper>
          </Dropdown>
        );
      },
    },
    {
      headerName: "Template",
      field: "templateName",
      minWidth: 100,
      flex: 1,
      cellRenderer: (params) => {
        const record = params?.data;

        return (
          <p className="template-container">
            {getTemplateIcon(record?.templateId)}
            {record.templateName}
          </p>
        );
      },
    },
    {
      headerName: "Path",
      field: "pathName",
      minWidth: 200,
      flex: 1,
      cellRenderer: ({ data }) => (
        <p className="right-align">
          {data?.pathName
            ? transformObjectPath(data?.pathName, data?.inTrash)
            : "-"}
        </p>
      ),
    },
    {
      headerName: "Date",
      field: "date",
      isDate: true,
      minWidth: 100,
      flex: 1,
      cellRenderer: ({ data }) => {
        return dayjs(data?.date).format("YYYY/MM/DD HH:mm");
      },
    },
  ];

  const [allColumnsRequest, setAllColumnsRequest] = useState([]);
  const [columns, setColumns] = useState(COLUMNS);

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs([...breadcrumb]));
  }, []);

  useEffect(() => {
    if (!localSettingsData && !templatesData) {
      return;
    }

    if (
      localSettingsData?.body[0]?.value?.favoritesTable?.columns &&
      localSettingsData?.body[0]?.value?.favoritesTable?.columns?.length > 0
    ) {
      const pinned =
        localSettingsData?.body[0]?.value?.favoritesTable?.pinned || [];
      const sort =
        localSettingsData?.body[0]?.value?.favoritesTable?.sort || [];

      const allColumns = [];
      localSettingsData.body[0].value.favoritesTable.columns?.forEach(
        (column) => {
          const index = COLUMNS.findIndex((item) => item.field === column);
          allColumns.push({
            ...COLUMNS[index],
            pinned: pinned?.includes(column) ? "left" : null,
            sort: sort?.find((val) => val.colId === column)?.sort || null,
          });
        }
      );
      setColumns([...allColumns]);
      setAllColumnsRequest(
        localSettingsData?.body[0]?.value?.favoritesTable?.columns
      );
      setPinnedColumns(
        localSettingsData?.body[0]?.value?.favoritesTable?.pinned
      );
      setSort(localSettingsData?.body[0]?.value?.favoritesTable?.sort);
      setFilters(localSettingsData?.body[0]?.value?.favoritesTable?.filters);
    } else {
      setColumns(COLUMNS);
      setAllColumnsRequest(COLUMNS?.map((col) => col.field));
    }

    setLoading(false);
  }, [localSettingsData, templatesData, trigger]);

  useEffect(() => {
    setPinnedItems(
      pinned?.map((item) => {
        return {
          ...item,
          date: new Date(item.date),
          templateId: Number(item?.templateId),
          templateName: getTemplateName(item?.templateId),
        };
      })
    );
  }, [pinned, localSettingsData]);

  const detectChange = () => {
    if (!mask) {
      dispatch(setMask(true));
    }
  };

  const handleNodeClick = async (key, id, name, parentId) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const baseUrl =
          import.meta.env.VITE_APP_BASE_URL === "/"
            ? ""
            : import.meta.env.VITE_APP_BASE_URL;

        window.open(
          `${window.origin}${baseUrl}/details/${parentId}?nodeId=${id}`
        );
      }
    }
  };

  const mutation = useMutation(saveLocalSettings, {
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setMask(false));
    },
  });

  const handleFavoritesClick = async (id: number) => {
    const parentPath = await getHierarchyDetails(id);
    dispatch(setRefreshBreadcrumbs(true));
    const parentIds = getParentIds(parentPath.path);
    dispatch(setExpandedKeys(parentIds));
  };

  const handleSave = async () => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        favoritesTable: {
          columns: allColumnsRequest,
          filters: filters,
          sort: sort,
          pinned: pinnedColumns,
        },
        pinned: [...pinned],
      },
    };
    await mutation.mutateAsync(request);

    queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
    showSuccessNotification("Changes published successfully!");
    dispatch(setMask(false));
  };

  const handleCancel = () => {
    setTrigger((trigger) => trigger + 1);
    setTimeout(() => {
      dispatch(setMask(false));
    }, 200);
  };

  const handleUnpinSelected = async () => {
    const newPinned = pinned?.filter(
      (item) =>
        selectedProducts?.findIndex((product) => product.id === item.id) === -1
    );

    await mutation.mutateAsync({
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        pinned: newPinned,
      },
    });
    showSuccessNotification("Selected items removed from pinned successfully!");
    dispatch(setPinned([...newPinned]));
    setPinnedItems(
      newPinned?.map((item) => {
        return {
          ...item,
          date: new Date(item.date),
          templateId: Number(item?.templateId),
        };
      })
    );
    setSelectedProducts([]);
  };

  const selectedActionsTemplate = (
    <PrimeButton
      rounded
      className="primary-button"
      onClick={handleUnpinSelected}
      disabled={!selectedProducts || !selectedProducts.length}
    >
      <PushpinFilled />
      {t("Unpin selected")}
    </PrimeButton>
  );

  if (isHome) {
    return (
      <MyTable
        onSelect={setSelectedProducts}
        loading={loading}
        height={height}
        columns={columns}
        resetTrigger={trigger}
        data={pinnedItems}
        setColumnsRequest={setAllColumnsRequest}
        detectChange={detectChange}
        excelFileName="favorites"
        setPinned={setPinnedColumns}
        setFilters={setFilters}
        setSort={setSort}
        initialFilters={
          localSettingsData?.body[0]?.value?.favoritesTable?.filters || {}
        }
      />
    );
  }

  return (
    <Suspense fallback={<PageTableSkeleton />}>
      <Wrapper theme={theme}>
        {contextHolder}
        <BreadCrumb
          extra={
            <Flex gap={10}>
              {mask && (
                <>
                  <Button
                    className="breadcrumb-button cancel-button"
                    onClick={handleCancel}
                  >
                    {t("Cancel")}
                  </Button>
                  <Button
                    className="breadcrumb-button save-button"
                    onClick={handleSave}
                    loading={mutation.isLoading}
                  >
                    {t("Save")}
                  </Button>
                </>
              )}
            </Flex>
          }
        />
        <div className={`content ${mask && "has-mask"}`}>
          <MyTable
            onSelect={setSelectedProducts}
            loading={loading}
            height={height}
            columns={columns}
            resetTrigger={trigger}
            data={pinnedItems}
            setColumnsRequest={setAllColumnsRequest}
            detectChange={detectChange}
            excelFileName="favorites"
            extra={selectedActionsTemplate}
            setPinned={setPinnedColumns}
            setFilters={setFilters}
            setSort={setSort}
            initialFilters={
              localSettingsData?.body[0]?.value?.favoritesTable?.filters || {}
            }
          />
        </div>
      </Wrapper>
      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
    </Suspense>
  );
};

export default FavoritesPage;

const breadcrumb = [
  {
    title: "Pinned",
    to: "/pinned",
  },
];

const NameWrapper = styled(Link)`
  display: flex;
  gap: 8px;
  align-items: center;
  text-align: left;
  font-size: 13px;
  width: fit-content;
  color: var(--color-text);
  text-decoration: none;
  &:hover {
    text-decoration: underline;
  }

  &.trash-hyperlink {
    color: var(--color-trash) !important;
    text-decoration: line-through !important;
  }

  & svg {
    min-width: 18px;
    height: 18px;
  }
`;
