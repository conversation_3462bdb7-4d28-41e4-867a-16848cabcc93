import React from "react";
import { render } from "@testing-library/react";
import { PathSeparator } from "./index";

describe("PathSeparator", () => {
  it("renders without crashing", () => {
    const { container } = render(<PathSeparator />);
    const svg = container.querySelector("svg");
    expect(svg).toBeInTheDocument();
  });

  it("has correct default styling", () => {
    const { container } = render(<PathSeparator />);
    const svg = container.querySelector("svg");
    
    expect(svg).toHaveStyle({
      width: "0.75em",
      height: "0.75em",
      stroke: "rgba(0, 0, 0, 0.8)",
    });
  });

  it("accepts custom styling", () => {
    const customStyle = { width: "1em", height: "1em" };
    const { container } = render(<PathSeparator style={customStyle} />);
    const svg = container.querySelector("svg");
    
    expect(svg).toHaveStyle({
      width: "1em",
      height: "1em",
    });
  });
});
