import React from "react";
import { i18n } from "../../utils/i18n";
import { PathSeparator } from "../../components/atoms/PathSeparator";

export const transformObjectPath = (
  path: string,
  inTrash: boolean
): React.ReactElement | string => {
  if (!path) {
    return "";
  }

  // Original cleanup logic - preserve this exactly
  const transformedText = path.replace(/{-?\d+}|[#|]/g, (match) =>
    match.startsWith("{") ? " > " : ""
  );

  // Split by > separator (handle any spacing inconsistencies)
  const parts = transformedText
    .split(/\s*>\s*/) // Split by > with optional spaces around it
    .map((part) => part.trim()) // Trim each part
    .filter((part) => part.length > 0); // Remove empty parts

  // Remove the last element if we have multiple parts
  if (parts.length > 1) {
    parts.pop();
  } else {
    // If only one part, return empty string or just "Trash" for trash items
    if (inTrash) {
      return (
        <span
          style={{
            display: "inline-flex",
            alignItems: "center",
            color: "rgba(0, 0, 0, 0.9)", // -10% lighter than pure black
            fontSize: "inherit",
            lineHeight: "inherit",
          }}
        >
          {i18n.t("Trash")}
        </span>
      );
    }
    return "";
  }

  // Create JSX elements with SVG separators
  const pathElements: React.ReactNode[] = [];

  // Add "Trash" prefix if needed
  if (inTrash) {
    pathElements.push(
      <span key="trash" style={{ color: "rgba(0, 0, 0, 0.9)" }}>
        {i18n.t("Trash")}
      </span>
    );
    pathElements.push(<PathSeparator key="trash-separator" />);
  }

  // Add path parts with separators
  parts.forEach((part, index) => {
    pathElements.push(
      <span key={`part-${index}`} style={{ color: "rgba(0, 0, 0, 0.9)" }}>
        {part}
      </span>
    );

    // Add separator after each part except the last one
    if (index < parts.length - 1) {
      pathElements.push(<PathSeparator key={`separator-${index}`} />);
    }
  });

  return (
    <span
      style={{
        display: "inline-flex",
        alignItems: "center",
        gap: "0.25em",
        fontSize: "inherit",
        lineHeight: "inherit",
        flexWrap: "wrap",
      }}
    >
      {pathElements}
    </span>
  );
};
