import { styled } from "@linaria/react";
import { But<PERSON>, Empty } from "antd";
import { SidebarTreeSkeleton, SearchInputWithFilter } from "../../atoms";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { AddNodeModal, ExportModal, RestoreNodeTemplateModal } from "../Modals";
import {
  AUTHOR_USERS_TEMPLATE_ID,
  GET_CHILDRENS,
  GET_HIERARCHY_DETAILS,
  GET_NODE_ATTRIBUTES_DETAILS,
  IActionMenus,
  TEMPLATES_ATTRIBUTE_TEMPLATE_ID,
} from "../../../constants";
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  getAllNodes,
  getAllNodesByTemplate,
  getHierarchyDetails,
  getNodeDetails,
  saveParentOrder,
  saveVisualOrder,
} from "../../../services/node";
import { DeleteNodeModal } from "../Modals/DeleteNodeModal";
import { useTheme } from "../../../utils/useTheme";
import { SidebarTree } from "../SidebarTree";

import { deepClone, getPathname } from "../../../utils";
import { IHierarchyData, INodeDetails, ITreeData } from "../../../interfaces";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import {
  setBreadcrumb,
  setMovingMask,
  setRefreshBreadcrumbs,
  setTempBreadcrumbs,
} from "../../../store/features";
import { setParentBreadcrumbs } from "../../../store/features/breadcrumbs";
import {
  setExpandedKeys,
  setFocusedNode,
  setMovePayload,
  setSelected,
  setBreadcrumbSort,
} from "../../../store/features/sidebar";
import { searchRecursivelyByKey } from "../../../utils/functions/recursives";
import { useTranslation } from "react-i18next";
import { useSidebarActions } from "../../../utils/functions/customHooks/useSidebarActions";
import { ResizableDiv } from "../ResizableDiv";
import { getParentIds } from "../../../utils/functions/getParentIds";
import { setPerformTrash } from "../../../store/features/trashcan";
import {
  setPublishWorkingVersion,
  setRemoveTemplate,
  setUpdateFlag,
} from "../../../store/features/workingVersion";
import {
  useFlags,
  useNotification,
} from "../../../utils/functions/customHooks";
import { searchRecursivelyByID } from "../../../utils/functions/sidebarActions";
// Import worker wrapper functions and fallback utils
import {
  compareTreeDataAndGeneratePayloadAsync,
  terminateWorker,
} from "../../../workers/visualOrderWorkerWrapper";
import {
  extractVisualOrderMap,
  generateMoveDataRecursively,
} from "../../../utils/functions/visualOrderExtractor";

export interface ISidebarAction {
  id?: string;
  key?: IActionMenus;
  label?: string;
  parentId?: string;
  title?: string;
  templateId?: number;
  allowedChildrens?: any;
}

type Props = {
  action: any;
  setAction: any;
  dropdownOpen: any;
  setDropdownOpen: any;
  treeData: ITreeData[];
  setTreeData: Dispatch<SetStateAction<ITreeData[]>>;
  closeEditMode?: any;
  fromDQM?: boolean;
};

const Sidebar = ({
  action,
  setAction,
  dropdownOpen,
  setDropdownOpen,
  closeEditMode,
  treeData,
  fromDQM,
  setTreeData,
}: Props) => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const params = useParams();
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();
  const location = useLocation();
  const pathname = getPathname();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const nodeId = searchParams.get("nodeId");
  const parentMenuNodeId = params?.nodeId;

  const [tempTreeData, setTempTreeData] = useState(null as any[] | null);
  const [initialLoad, setInitialLoad] = useState(true);
  const [loading, setLoading] = useState(true);

  const metamodel = location.pathname.includes("metamodel");
  const dataSource = location.pathname.includes("data-sources");

  const {
    mask,
    expandedKeys,
    focusedNode,
    selected,
    movePayload,
    attributeMask,
    breadcrumbSort,
  } = useSelector((state: RootState) => state.sidebar);

  const { performTrash } = useSelector((state: RootState) => state.trash);

  const workingVersionActive = useSelector(
    (state: RootState) => state.mask.workingVersion
  );
  const movingMask = useSelector((state: RootState) => state.mask.movingMask);

  const { refreshBreadcrumbs, tempBreadcrumbs, parentBreadcrumbs } = useSelector(
    (state: RootState) => state.breadcrumbs
  );

  const { authenticated } = useSelector((state: RootState) => state.auth);

  const { publishWorkingVersion, toUpdateFlag, removeTemplate } = useSelector(
    (state: RootState) => state.workingVersion
  );

  const {
    updateChildNode,
    generateDataRecursively,
    generateSearchDataRecursively,
    generateTotalDataRecursively,
    handleNodeDelete,
    updateNodeFromTrash,
    updateNodeAfterPublish,
    restoreTemplates,
    updateDisabledTemplates,
  } = useSidebarActions();
  const { getFlags } = useFlags();

  useEffect(() => {
    setLoading(true);
  }, [parentMenuNodeId]);

  const { isLoading, data, isError } = useQuery<ITreeData[], Error>(
    [GET_CHILDRENS, parentMenuNodeId?.toString()],
    () => {
      // Check if this is authors context (nodeId = AUTHOR_USERS_TEMPLATE_ID)
      if (
        parentMenuNodeId?.toString() === AUTHOR_USERS_TEMPLATE_ID.toString()
      ) {
        return getAllNodesByTemplate([AUTHOR_USERS_TEMPLATE_ID]);
      }
      return getAllNodes(parentMenuNodeId);
    },
    {
      staleTime: 1000 * 60 * 5, // Data remains fresh for 5 minutes
      cacheTime: 1000 * 60 * 10,
      enabled: !!authenticated && !!parentMenuNodeId,
    }
  );

  useEffect(() => {
    if (!searchParams.get("redirect")) {
      const formatData = async () => {
        // generating tree data
        const formattedTreeData = await generateDataRecursively(data);
        setTreeData([...formattedTreeData]);
        setLoading(false);

        let selectedNodeKey = null;
        const focusedItemIndex =
          Object.keys(focusedNode).indexOf(parentMenuNodeId);
        if (focusedItemIndex !== -1) {
          selectedNodeKey = Object.values(focusedNode)[focusedItemIndex];
        } else {
          selectedNodeKey = formattedTreeData[0]?.key;
        }

        if (selectedNodeKey && !nodeId) {
          const selectedNode = formattedTreeData[0];
          dispatch(setBreadcrumb([...(selectedNode?.breadcrumb || [])]));
          navigate(`${pathname}?nodeId=${selectedNodeKey}`, { replace: true });
          dispatch(
            setSelected({
              keys: [selectedNode?.key],
              info: [
                {
                  id: selectedNode?.key,
                  isAsset: false,
                  name: selectedNode?.name,
                  parentId: selectedNode?.parentId,
                  templateId: selectedNode?.templateId,
                  body: selectedNode?.body,
                  isLeaf: selectedNode?.isLeaf,
                },
              ],
            })
          );
        }
        dispatch(setExpandedKeys([...expandedKeys]));
      };
      if (data) {
        formatData();
      } else {
        setTreeData([]);
      }
    }
  }, [data, searchParams.get("redirect")]);

  const { data: hierarchyData } = useQuery(
    [GET_HIERARCHY_DETAILS, nodeId],
    () => getHierarchyDetails(nodeId),
    {
      enabled:
        !!authenticated &&
        !!nodeId &&
        !!treeData &&
        !searchParams.get("draft") &&
        initialLoad,

      onSuccess: async (hierarchyData: IHierarchyData) => {
        const focusedNodes = { ...focusedNode };
        focusedNodes[parentMenuNodeId] = nodeId;
        dispatch(setFocusedNode(focusedNodes));

        const parentIds = getParentIds(hierarchyData.path);

        const indexOfCurrentNode = parentIds.findIndex(
          (item) => item === Number(parentMenuNodeId)
        );

        if (indexOfCurrentNode !== -1) {
          // to highlight selected hierarchy in trees on reload
          const hierarchy =
            metamodel || dataSource
              ? parentIds
              : parentIds.slice(indexOfCurrentNode);

          setTimeout(() => {
            if (hierarchy.length > 0) {
              dispatch(setExpandedKeys([...expandedKeys, ...hierarchy]));
            }
          }, 400);
        }
        setInitialLoad(false);
      },
    }
  );

  useEffect(() => {
    if (hierarchyData && searchParams.get("redirect")) {
      const formatData = async () => {
        const parentIds = getParentIds(hierarchyData?.path);
        const indexOfCurrentNode = parentIds.findIndex(
          (item) => item === Number(parentMenuNodeId)
        );

        if (indexOfCurrentNode !== -1) {
          // to highlight selected hierarchy in trees on reload
          const hierarchy = metamodel
            ? parentIds
            : parentIds.slice(indexOfCurrentNode);
          let data = queryClient.getQueryData([
            GET_CHILDRENS,
            hierarchy?.join(),
          ]) as ITreeData[];
          if (!data) {
            data = (await getAllNodes(hierarchy?.join())) as ITreeData[];
            queryClient.setQueryData([GET_CHILDRENS, hierarchy?.join()], data);
          }
          const formattedTreeData = generateTotalDataRecursively(
            data,
            data,
            parentMenuNodeId
          );
          setTreeData([...formattedTreeData]);
          setLoading(false);

          let selectedNodeKey = null;
          const focusedItemIndex =
            Object.keys(focusedNode).indexOf(parentMenuNodeId);
          if (focusedItemIndex !== -1) {
            selectedNodeKey = Object.values(focusedNode)[focusedItemIndex];
          } else {
            selectedNodeKey = formattedTreeData[0]?.key;
          }

          dispatch(setExpandedKeys([...hierarchy]));
          if (selectedNodeKey && !nodeId) {
            const selectedNode = formattedTreeData[0];
            dispatch(setBreadcrumb([...(selectedNode?.breadcrumb || [])]));
            navigate(`${pathname}?nodeId=${selectedNodeKey}`, {
              replace: true,
            });
            dispatch(
              setSelected({
                keys: [selectedNode?.key],
                info: [
                  {
                    id: selectedNode?.key,
                    isAsset: false,
                    name: selectedNode?.name,
                    parentId: selectedNode?.parentId,
                    templateId: selectedNode?.templateId,
                    body: selectedNode?.body,
                    isLeaf: selectedNode?.isLeaf,
                  },
                ],
              })
            );
          }
        } else {
          setTreeData([]);
        }
      };
      if (searchParams.get("redirect")) {
        formatData();
      }
    }
  }, [data, searchParams.get("redirect"), hierarchyData]);

  const handleAfterPublish = async () => {
    const nodeDetails = queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      searchParams.get("nodeId"),
    ]) as INodeDetails;
    let nodeId = searchParams.get("nodeId");
    if (nodeDetails.templateId === TEMPLATES_ATTRIBUTE_TEMPLATE_ID) {
      nodeId = nodeDetails.parentId;
      queryClient.invalidateQueries([
        GET_NODE_ATTRIBUTES_DETAILS,
        nodeDetails.id.toString(),
      ]);
    }
    await getNodeDetails(nodeId).then(async (data) => {
      queryClient.setQueryData(
        [GET_NODE_ATTRIBUTES_DETAILS, nodeId.toString()],
        data
      );
      await getAllNodes(nodeId).then(async (childrens: any) => {
        queryClient.setQueryData([GET_CHILDRENS, nodeId.toString()], childrens);

        const newTreeData = updateNodeAfterPublish(treeData, data, childrens);
        setTreeData([...newTreeData]);
        dispatch(setPublishWorkingVersion(false));
        dispatch(setExpandedKeys([...expandedKeys, Number(nodeId)]));

        await getAllNodes(selected.info[0]?.parentId.toString()).then(
          (childrens: any) => {
            queryClient.setQueryData(
              [GET_CHILDRENS, selected.info[0]?.parentId.toString()],
              childrens
            );
          }
        );
      });
    });
  };

  const handleUpdateFlag = (
    data: ITreeData[],
    newFlag: string[],
    id: number
  ) => {
    const findAndUpdate = (items) => {
      for (const item of items) {
        if (item.key === id) {
          item.flag = newFlag;
          return true;
        }

        if (item.children && item.children.length > 0) {
          if (findAndUpdate(item.children)) {
            return true;
          }
        }
      }
      return false;
    };

    findAndUpdate(data);
  };

  const removeNewTemplateAfterWorkingVersionDelete = (tree, targetId) => {
    return tree.reduce((acc, node) => {
      if (node.key == targetId) {
        return acc;
      }

      if (node.children) {
        node.children = removeNewTemplateAfterWorkingVersionDelete(
          node.children,
          targetId
        );
      }

      acc.push(node);
      return acc;
    }, []);
  };

  useEffect(() => {
    if (removeTemplate) {
      const updatedTree = removeNewTemplateAfterWorkingVersionDelete(
        treeData,
        searchParams?.get("nodeId")
      );
      setTreeData(updatedTree);
      dispatch(setRemoveTemplate(null));
      navigate(`${pathname}?nodeId=${updatedTree[0]?.key}`);
      dispatch(setRefreshBreadcrumbs(true));
    }
  }, [removeTemplate]);

  useEffect(() => {
    setTempTreeData(null);
  }, [params?.nodeId]);

  useEffect(() => {
    if (toUpdateFlag) {
      const details = queryClient.getQueryData([
        GET_NODE_ATTRIBUTES_DETAILS,
        searchParams.get("nodeId"),
      ]) as any;

      const newTreeData = [...treeData];
      handleUpdateFlag(
        newTreeData,
        getFlags(details?.bitFlag),
        Number(searchParams.get("nodeId"))
      );
      setTreeData([...newTreeData]);
      dispatch(setUpdateFlag(false));
    }
  }, [toUpdateFlag]);

  useEffect(() => {
    if (publishWorkingVersion) {
      handleAfterPublish();
    }
  }, [publishWorkingVersion, treeData]);

  useEffect(() => {
    if (
      treeData.length > 0 &&
      (selected?.keys?.length === 0 ||
        refreshBreadcrumbs ||
        searchParams.get("draft") ||
        searchParams.get("redirect"))
    ) {
      const selectedNode = searchRecursivelyByKey(treeData, nodeId, "key");
      if (selectedNode) {
        dispatch(setBreadcrumb([...(selectedNode?.breadcrumb || [])]));

        dispatch(
          setSelected({
            keys: [selectedNode?.key],
            info: [
              {
                id: selectedNode?.key,
                isAsset: false,
                name: selectedNode?.name,
                parentId: selectedNode?.parentId,
                templateId: selectedNode?.templateId,
                body: selectedNode?.body,
                isLeaf: selectedNode?.isLeaf,
              },
            ],
          })
        );
        const focusedNodes = { ...focusedNode };
        focusedNodes[params?.nodeId] = selectedNode.key;
        dispatch(setFocusedNode(focusedNodes));

        dispatch(setRefreshBreadcrumbs(false));
      }
    }
  }, [
    treeData,
    params?.nodeId,
    nodeId,
    refreshBreadcrumbs,
    searchParams.get("draft"),
    searchParams.get("redirect"),
  ]);

  const checkForAssetNode = () => {
    let hasAssetNode = false;
    selected?.info?.forEach((item) => {
      if (item?.isAsset) {
        hasAssetNode = true;
      }
    });
    return hasAssetNode;
  };

  // const handleTemplateAdd = (event) => {
  //   setAction({
  //     id: params?.nodeId,
  //     key: "add-document-group",
  //     title: templateMenu.find((elm) => elm.key == event.key)?.label || null,
  //     templateId: event.key,
  //     fromHeader: true,
  //   });
  // };

  const bottomNavigationMask = useSelector(
    (state: RootState) => state.mask.bottomDrawer
  );

  const updateTreeFromTrash = async () => {
    const toUpdateNodeId = performTrash?.treeNode;
    const selectedNode = searchRecursivelyByKey(
      treeData,
      toUpdateNodeId,
      "key"
    );
    if (selectedNode) {
      const updatedTree = [...treeData];

      if (!selectedNode.isLeaf && selectedNode.children.length === 0) {
        // dispatch(setExpandedKeys([...expandedKeys, toUpdateNodeId]));
        dispatch(setExpandedKeys([...expandedKeys, toUpdateNodeId]));
        await getAllNodes(toUpdateNodeId).then((response: any) => {
          queryClient.setQueryData(
            [GET_CHILDRENS, toUpdateNodeId.toString()],
            response
          );

          setTimeout(() => {
            updateNodeFromTrash(updatedTree, toUpdateNodeId);
          }, 900);
        });
      } else {
        dispatch(setExpandedKeys([...expandedKeys, toUpdateNodeId]));
        updateNodeFromTrash(updatedTree, toUpdateNodeId);
      }
      setTreeData(updatedTree);

      dispatch(setPerformTrash(null));
    }
  };

  useEffect(() => {
    if (performTrash) {
      updateTreeFromTrash();
    }
  }, [performTrash, treeData]);

  // Using utility functions from utils.js for optimized visual order updates

  const saveParentVisualOrderMutation = useMutation(saveParentOrder, {
    onError: () => {
      showErrorNotification("Error in changing parent!");
    },
  });

  const saveVisualOrderMutation = useMutation(saveVisualOrder, {
    onSuccess: () => {
      showSuccessNotification("Visual order saved successfully!");
      dispatch(setMovingMask(false));
    },
    onError: () => {
      showErrorNotification("Error in saving visual order!");
    },
  });

  /**
   * Optimized function to save visual order changes
   * Uses web workers to offload heavy calculations and prevent UI blocking
   * Only sends nodes that have actually changed position
   */
  const handleMoveSave = async () => {
    try {
      // First handle parent changes if any
      if (Object.keys(movePayload).length > 0) {
        await saveParentVisualOrderMutation.mutateAsync(movePayload);
      }

      if (!tempTreeData) {
        dispatch(setMovingMask(false));
        return;
      }

      let payload: Record<string, number> = {};

      try {
        payload = await compareTreeDataAndGeneratePayloadAsync(
          tempTreeData,
          treeData,
          movePayload
        );
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (workerError) {
        const originalOrderMap = extractVisualOrderMap(tempTreeData);
        payload = generateMoveDataRecursively(
          treeData,
          originalOrderMap,
          {},
          movePayload
        );
      }

      // Only send the API request if there are actual changes
      if (Object.keys(payload).length > 0) {
        saveVisualOrderMutation.mutate(payload);
      } else {
        dispatch(setMovingMask(false));
      }
    } catch {
      showErrorNotification("Error processing visual order changes");
      dispatch(setMovingMask(false));
    }
  };

  useEffect(() => {
    if (searchParams.get("sort"))
      applySort(searchParams.get("sort"), selected.keys[0]);
  }, [searchParams.get("sort")]);

  // Listen for breadcrumb sort action
  useEffect(() => {
    if (breadcrumbSort) {
      applySort(breadcrumbSort, null); // null indicates top-level sorting
      dispatch(setBreadcrumbSort(null)); // Clear the action
    }
  }, [breadcrumbSort]);

  // Cleanup worker when component unmounts
  useEffect(() => {
    return () => {
      // Terminate the worker when the component unmounts
      terminateWorker();
    };
  }, []);

  const handleSort = async (key: string): Promise<void> => {
    const parentId = selected.keys[0];
    dispatch(setExpandedKeys([...expandedKeys, parentId]));
    const selectedKey = searchRecursivelyByID(treeData, parentId);
    const notLoaded =
      !selectedKey?.isLeaf && selectedKey?.children?.length === 0;
    if (notLoaded) {
      const newParams = new URLSearchParams(searchParams);
      newParams.set("isSorting", key);
      setSearchParams(newParams);
    } else {
      applySort(key, parentId);
    }
  };

  const applySort = (key: string, parentId: string | number | null): void => {
    dispatch(setMovingMask(true));

    setTempTreeData([...treeData]);

    const sortFunction =
      key === "sort-asc"
        ? sortTreeAscending
        : key === "sort-desc"
          ? sortTreeDescending
          : key === "sort-visual-order"
            ? sortTreeByVisualOrder
            : null;
    if (!sortFunction) return;

    // Handle top-level sorting (breadcrumb sort) when parentId is null
    if (parentId === null) {
      const sortedTreeData = sortFunction([...treeData]);
      setTreeData(sortedTreeData);
    } else {
      // Handle regular node children sorting
      const sortChildren = (nodes: ITreeData[]): ITreeData[] => {
        return nodes.map((node) => {
          if (node.key == parentId && node.children) {
            return { ...node, children: sortFunction([...node.children]) };
          }
          return node.children
            ? { ...node, children: sortChildren(node.children) }
            : node;
        });
      };
      setTreeData(sortChildren([...treeData]));
    }

    if (searchParams.get("sort")) {
      const newParams = new URLSearchParams(searchParams);
      newParams.delete("sort");
      setSearchParams(newParams);
    }
  };

  function sortTreeByVisualOrder(tree: ITreeData[]): ITreeData[] {
    return tree
      .map((node) => ({
        ...node,
        children: node.children
          ? sortTreeByVisualOrder([...node.children])
          : [],
      }))
      .sort((a, b) => a.visualOrder - b.visualOrder);
  }

  function sortTreeAscending(tree: ITreeData[]): ITreeData[] {
    return tree
      .map((node) => ({
        ...node,
        children:
          node.children?.length > 0
            ? sortTreeAscending([...node.children])
            : [],
      }))
      .sort((a, b) =>
        a.name.localeCompare(b.name, undefined, { numeric: true })
      );
  }

  function sortTreeDescending(tree: ITreeData[]): ITreeData[] {
    return tree
      .map((node) => ({
        ...node,
        children: node.children ? sortTreeDescending([...node.children]) : [],
      }))
      .sort((a, b) =>
        b.name.localeCompare(a.name, undefined, { numeric: true })
      );
  }

  const findNodeByName = (
    nodes: ITreeData[],
    targetName: string
  ): ITreeData | null => {
    for (const node of nodes) {
      if (node.name === targetName) {
        return node;
      }
      if (node.children) {
        const result = findNodeByName(node.children, targetName);
        if (result) return result;
      }
    }
    return null;
  };

  useEffect(() => {
    // for browser back click
    if (!selected.keys?.includes(Number(searchParams.get("nodeId")))) {
      const selectedNode = searchRecursivelyByKey(treeData, nodeId, "key");
      if (selectedNode) {
        dispatch(setBreadcrumb([...(selectedNode?.breadcrumb || [])]));

        dispatch(
          setSelected({
            keys: [selectedNode?.key],
            info: [
              {
                id: selectedNode?.key,
                isAsset: false,
                name: selectedNode?.name,
                parentId: selectedNode?.parentId,
                templateId: selectedNode?.templateId,
                body: selectedNode?.body,
                isLeaf: selectedNode?.isLeaf,
              },
            ],
          })
        );
        const focusedNodes = { ...focusedNode };
        focusedNodes[params?.nodeId] = selectedNode.key;
        dispatch(setFocusedNode(focusedNodes));

        dispatch(setRefreshBreadcrumbs(false));
      }
    }
  }, [searchParams.get("nodeId")]);

  return (
    <ResizableDiv
      defaultWidth="fit-content"
      maxWidth={"70%"}
      minWidth={18}
      resize="right"
      style={
        mask || attributeMask || bottomNavigationMask || workingVersionActive
          ? { borderRight: "2px solid #505a6240" }
          : { borderRight: "2px solid white" }
      }
    >
      {contextHolder}
      {(mask ||
        bottomNavigationMask ||
        attributeMask ||
        workingVersionActive) && <Mask className="mask" />}
      <Container
        style={{
          border: movingMask ? "1px solid red" : "unset",
          borderRight: movingMask ? "1px solid red" : "1px solid #efefef",
        }}
        onClick={() => {
          closeEditMode();
          if (selected.keys.length > 1) {
            const onlySelectedCurrentNode = selected.info.filter(
              (item) => item.id === Number(nodeId)
            );
            dispatch(setBreadcrumb(tempBreadcrumbs));
            dispatch(setTempBreadcrumbs(null));
            dispatch(
              setSelected({
                keys: [Number(nodeId)],
                info: onlySelectedCurrentNode,
              })
            );
          }
        }}
        theme={theme}
      >
        {movingMask && (
          <div className="buttons">
            <Button
              className="cancel-button"
              type="primary"
              onClick={() => {
                dispatch(setMovingMask(false));
                dispatch(setMovePayload({}));
                setTreeData([...tempTreeData]);
              }}
            >
              {t("Cancel")}
            </Button>
            <Button
              type="primary"
              className="save-button"
              onClick={handleMoveSave}
              loading={
                saveVisualOrderMutation.isLoading ||
                saveParentVisualOrderMutation.isLoading
              }
            >
              {t("Save")}
            </Button>
          </div>
        )}
        <SearchWrapper theme={theme}>
          <SearchInputWithFilter
            parentId={params?.nodeId}
            onSearch={(searchResponse: any, searchText: string) => {
              dispatch(setBreadcrumb([]));

              if (!searchText && tempTreeData) {
                setTreeData([...tempTreeData]);
                if (tempTreeData?.length > 0) {
                  const firstNode = tempTreeData[0];
                  dispatch(
                    setSelected({
                      keys: [firstNode?.key],
                      info: [
                        {
                          id: firstNode?.key,
                          name: firstNode?.name,
                          parentId: firstNode?.parentId,
                          templateId: firstNode?.templateId,
                        },
                      ],
                    })
                  );
                  const newParams = new URLSearchParams(searchParams);
                  newParams.set("nodeId", firstNode?.key);
                  setSearchParams(newParams);

                  dispatch(setBreadcrumb(firstNode?.breadcrumb));
                }
                setTempTreeData(null);
                return;
              }
              const searchTreeData =
                generateSearchDataRecursively(searchResponse);
              const matchedNode = findNodeByName(searchTreeData, searchText);
              if (matchedNode) {
                dispatch(
                  setExpandedKeys([...expandedKeys, matchedNode?.parentId])
                );
                dispatch(
                  setSelected({
                    keys: [matchedNode?.key],
                    info: [
                      {
                        id: matchedNode?.key,
                        name: matchedNode?.name,
                        parentId: matchedNode?.parentId,
                        templateId: matchedNode?.templateId,
                      },
                    ],
                  })
                );
                const newParams = new URLSearchParams(searchParams);
                newParams.set("nodeId", String(matchedNode?.key));
                setSearchParams(newParams);

                dispatch(setBreadcrumb(matchedNode?.breadcrumb));
              } else {
                dispatch(setSelected({ keys: [], info: [] }));
              }
              if (!tempTreeData) setTempTreeData([...treeData]);

              setTreeData(searchTreeData);
            }}
          />
          {/* {globalPermissions.includes("SORT") && (
            <Dropdown menu={{ items: SORT_OPTIONS, onClick: handleSort }}>
              <div className="sort">
                <i className="pi pi-sort-alt" />
              </div>
            </Dropdown>
          )} */}
        </SearchWrapper>
        {isLoading || loading ? (
          <SidebarTreeSkeleton />
        ) : (
          <SidebarWrapper>
            {isError ? (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={t("Error in fetching data!")}
              />
            ) : treeData && treeData?.length === 0 ? (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={t("No data")}
              />
            ) : (
              <SidebarTree
                setAction={setAction}
                dropdownOpen={dropdownOpen}
                setDropdownOpen={setDropdownOpen}
                treeData={treeData}
                setTreeData={setTreeData}
                fromDQM={fromDQM}
                setTempTreeData={setTempTreeData}
                tempTreeData={tempTreeData}
                handleSort={handleSort}
                disabled={
                  saveVisualOrderMutation.isLoading ||
                  saveParentVisualOrderMutation.isLoading
                }
              />
            )}
          </SidebarWrapper>
        )}
      </Container>

      <ExportModal
        isOpen={action.key === "export"}
        onClose={() => setAction({ key: null })}
      />

      {(action.key === "add-document-group" ||
        action.key === "rename" ||
        action.key === "add-asset" ||
        action.key === "update-asset" ||
        action?.isOpen ||
        action?.key?.startsWith("add-with-template")) && (
          <AddNodeModal
            nodeType={3}
            metamodel={metamodel || dataSource}
            edit={action.key === "rename"}
            id={action.id}
            treeData={treeData}
            templateIds={
              action?.key === "add-asset" || action?.key === "update-asset"
                ? action.allowedChildrens
                : action?.templateId
            }
            parentId={action?.parentId}
            isAsset={
              action?.key === "add-asset" || action?.key === "update-asset"
            }
            afterSave={(
              tempGeneratedID,
              nodeName,
              selectedTemplateIcon,
              allowedChildrens
            ) => {
              const updatedTree = [...treeData];
              updateChildNode(
                updatedTree,
                tempGeneratedID,
                nodeName,
                selectedTemplateIcon,
                action,
                allowedChildrens
              );
              setTreeData(updatedTree);
              // if (action.key === "rename" && (metamodel || dataSource)) {
              //   queryClient.invalidateQueries(["get-all-templates"]);
              // }
            }}
            label={action.label}
            isOpen={
              action.key === "add-document-group" ||
              action.key === "rename" ||
              action?.key?.startsWith("add-with-template") ||
              action.key === "add-asset" ||
              action.key === "update-asset" ||
              action?.isOpen
            }
            title={action.title}
            onClose={() => {
              setDropdownOpen(false);
              setAction({ key: null });
            }}
          />
        )}

      {action?.key?.startsWith("delete") && (
        <DeleteNodeModal
          isOpen={action?.key?.startsWith("delete")}
          id={action.id}
          action={action}
          templateId={action?.templateId}
          hasAssetTypeNode={checkForAssetNode()}
          afterDelete={async () => {
            const updatedTree = [...treeData];
            handleNodeDelete(
              updatedTree,
              metamodel && action?.key === "delete"
            );

            if (metamodel) {
              await updateDisabledTemplates(updatedTree);
              setTreeData(updatedTree);
            } else {
              setTreeData(updatedTree);
            }
          }}
          label={action.label}
          isMultiple={selected.keys.length > 1}
          onClose={() => {
            setDropdownOpen(false);
            setAction({ key: null });
          }}
        />
      )}

      {action?.key?.startsWith("restore-template") && (
        <RestoreNodeTemplateModal
          isOpen={action?.key?.startsWith("restore-template")}
          id={action.id}
          templateId={action?.templateId}
          afterDelete={async (restoredIds) => {
            const updatedTree = [...treeData];

            handleNodeDelete(updatedTree, metamodel);
            setTimeout(async () => {
              const newData = deepClone(updatedTree);
              await restoreTemplates(newData, restoredIds);
              setTreeData(newData);
            }, 400);
          }}
          label={action.label}
          isMultiple={selected.keys.length > 1}
          onClose={() => {
            setDropdownOpen(false);
            setAction({ key: null });
          }}
        />
      )}
    </ResizableDiv>
  );
};

export { Sidebar };

const SearchWrapper = styled.div<{ theme: any }>`
  display: flex;
  margin-bottom: 10px;
  margin-right: 10px;
  gap: 10px;

  & .search-wrapper {
    flex: 1;
  }

  & .sort {
    cursor: pointer;
    background-color: ${({ theme }) => theme.bgAttributes};
    color: ${({ theme }) => theme.colorPrimary};
    display: flex;
    align-items: center;
    padding: 0px 6px;
    border-radius: 4px;
  }
`;

const Mask = styled.div`
  height: 100%;
  width: 100%;
  z-index: 100;
  top: 0px;
  left: 0px;
`;

const SidebarWrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  padding-right: 10px;

  & .ant-tree-list-holder {
    /* overflow-y: auto !important ; */
    & > div {
      margin-bottom: 4px;
    }
  }
  & .search-wrapper {
    margin-top: auto;
    margin-left: 6px;
    position: sticky;
    bottom: 0px;
    z-index: 1;
  }
`;

const Container = styled.aside<{ theme: any }>`
  background-color: #fff;
  padding: 8px 0px 0px 12px;
  position: relative;
  height: 99.5%;

  & .buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    justify-content: end;
    margin-right: 10px;

    & button {
      font-size: 13px;
      padding: 0px 10px;
      height: 24px;
    }
  }

  overflow-x: auto;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #efefef;
  & .ant-empty-description {
    font-size: 12px;
  }
  & .check-link {
    text-align: right;
    font-size: 11px;
    color: #4277a2;
    text-decoration: underline;
    margin-bottom: 6px;
    margin-top: -3px;
    cursor: pointer;
  }

  & .ant-tree,
  .ant-tree-list,
  .ant-tree-list-holder {
    height: 100%;
  }

  & .ant-tree-switcher-icon {
    background-color: #fff;

    & path {
      pointer-events: none;
    }
  }

  & .ant-empty-description > a {
    font-size: 12px;
    display: flex;
    justify-content: center;
    gap: 4px;
    color: rgba(0, 0, 0, 0.25);
  }
  & .ant-tree-title {
    flex: 1;
  }

  & .ant-tree-switcher > svg {
    width: 20px;
    transition: all 0.2s ease-in;
    height: fit-content;

    & path {
      fill: ${({ theme }) => theme.colorPrimary};
    }
  }

  & .ant-tree-iconEle {
    margin-right: 6px;
    & img {
      width: 18px;
      height: 18px;
      object-fit: contain;
    }
    & svg {
      width: 18px;
      height: 18px;
    }
  }

  & .ant-dropdown-open {
    opacity: 1 !important;
  }
`;
