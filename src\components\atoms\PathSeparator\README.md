# PathSeparator Component

A reusable Ant Design icon component that renders a right-chevron icon for use as a path separator in breadcrumb-style navigation displays.

## Features

- **Ant Design Integration**: Uses `RightOutlined` icon from @ant-design/icons
- **Responsive sizing**: Uses `em` units to scale with parent font size
- **Consistent styling**: Uses Ant Design's gray-6 color (#8c8c8c)
- **Flexible styling**: Accepts custom styles and className props
- **Inline display**: Designed to work inline with text content

## Usage

```tsx
import { PathSeparator } from "../../components/atoms/PathSeparator";

// Basic usage
<PathSeparator />

// With custom styling
<PathSeparator style={{ fontSize: "1em", color: "#666" }} />

// With custom className
<PathSeparator className="custom-separator" />
```

## Integration with transformObjectPath

This component is automatically used by the `transformObjectPath` function to replace text-based " > " separators with Ant Design icons. All existing components using `transformObjectPath` will automatically benefit from the new visual separators.

## Styling

- **Default size**: 0.75em (scales with font size)
- **Color**: #8c8c8c (Ant Design's gray-6)
- **Display**: inline-flex with center alignment and flex-shrink: 0

## Dependencies

- @ant-design/icons (RightOutlined)

## Browser Support

Works in all modern browsers supported by Ant Design.
