# PathSeparator Component

A reusable SVG component that renders a chevron-right icon for use as a path separator in breadcrumb-style navigation displays.

## Features

- **Responsive sizing**: Uses `em` units to scale with parent font size
- **Optimized colors**: Uses `rgba(0, 0, 0, 0.8)` for 20% lighter appearance than pure black
- **Flexible styling**: Accepts custom styles and className props
- **Inline display**: Designed to work inline with text content

## Usage

```tsx
import { PathSeparator } from "../../components/atoms/PathSeparator";

// Basic usage
<PathSeparator />

// With custom styling
<PathSeparator style={{ width: "1em", height: "1em" }} />

// With custom className
<PathSeparator className="custom-separator" />
```

## Integration with transformObjectPath

This component is automatically used by the `transformObjectPath` function to replace text-based " > " separators with SVG icons. All existing components using `transformObjectPath` will automatically benefit from the new visual separators.

## Styling

- **Default size**: 0.75em × 0.75em (scales with font size)
- **Color**: rgba(0, 0, 0, 0.8) - 20% lighter than pure black
- **Stroke width**: 2px
- **Display**: inline-block with flex-shrink: 0

## Browser Support

Works in all modern browsers that support SVG and CSS flexbox.
